package models

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type MemberTempPassword struct {
	ID           uint      `gorm:"primaryKey" json:"id"`
	MemberID     uint      `gorm:"not null;index" json:"member_id"`
	TempPassword string    `gorm:"not null" json:"temp_password"`
	ExpiredAt    time.Time `gorm:"not null;index" json:"expired_at"`
	UsedAt       null.Time `gorm:"index" json:"used_at"`
	CreatedAt    time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// Relations
	Member Member `gorm:"foreignKey:MemberID" json:"member,omitempty"`
}

func (MemberTempPassword) TableName() string {
	return "member_temp_passwords"
}

// GenerateRandomPassword 生成隨機密碼
func GenerateRandomPassword(length int) (string, error) {
	const charset = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
	password := make([]byte, length)
	
	for i := range password {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", err
		}
		password[i] = charset[num.Int64()]
	}
	
	return string(password), nil
}

// CreateTempPassword 為會員創建臨時密碼
func CreateTempPassword(conn *gorm.DB, memberID uint, expiredMinutes int) (*MemberTempPassword, error) {
	// 生成8位隨機密碼
	tempPassword, err := GenerateRandomPassword(8)
	if err != nil {
		return nil, fmt.Errorf("生成臨時密碼失敗: %v", err)
	}

	// 設定過期時間
	expiredAt := time.Now().Add(time.Duration(expiredMinutes) * time.Minute)

	tempPwd := &MemberTempPassword{
		MemberID:     memberID,
		TempPassword: tempPassword,
		ExpiredAt:    expiredAt,
	}

	if err := conn.Create(tempPwd).Error; err != nil {
		return nil, fmt.Errorf("創建臨時密碼失敗: %v", err)
	}

	return tempPwd, nil
}

// ValidateTempPassword 驗證臨時密碼
func ValidateTempPassword(conn *gorm.DB, memberID uint, tempPassword string) (*MemberTempPassword, error) {
	var tempPwd MemberTempPassword
	
	err := conn.Where("member_id = ? AND temp_password = ? AND used_at IS NULL AND expired_at > ?", 
		memberID, tempPassword, time.Now()).First(&tempPwd).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("臨時密碼無效或已過期")
		}
		return nil, fmt.Errorf("驗證臨時密碼失敗: %v", err)
	}

	return &tempPwd, nil
}

// MarkAsUsed 標記臨時密碼為已使用
func (tmp *MemberTempPassword) MarkAsUsed(conn *gorm.DB) error {
	tmp.UsedAt = null.TimeFrom(time.Now())
	return conn.Save(tmp).Error
}

// CleanupExpiredPasswords 清理過期的臨時密碼
func CleanupExpiredPasswords(conn *gorm.DB) error {
	return conn.Where("expired_at < ?", time.Now()).Delete(&MemberTempPassword{}).Error
}
