async function updateSetting(debug = false) {
    
    const form = document.forms.main_form;
    const btns = form.querySelectorAll('button');
    const validator = $(form).validate();

    if (!validator.checkForm()) {
        validator.showErrors();
        validator.focusInvalid();
        return;
    }

    msgLoading();

    btns.forEach(btn => {
        btn.disabled = true;
    })

    await axiosRequest()
        .post('/api/admin/settings', {
            com_name: form.com_name.value,
            com_email: form.com_email.value,
            bcc_mail: form.bcc_mail.value,
            pic_url: form.pic_url.value,
            smtp_ip: form.smtp_ip.value,
            smtp_port: form.smtp_port.value,
            smtp_ssl: form.smtp_ssl.checked ? 1 : 0,
            use_sender: form.use_sender.checked ? 1 : 0,
            smtp_user: form.smtp_user.value,
            smtp_pwd: form.smtp_pwd.value,
            smtp_def: form.smtp_def.value,
        })
        .then(res => {
            console.log(res);
            msgTopSuccess();
        })
        .catch(err => {
            msgError(err.response.data.message);
        })
        .finally(() => {
            btns.forEach(btn => {
                btn.disabled = false;
            })
        })

    if (debug) {
        axiosRequest()
            .get('/api/admin/smtp/debug')
            .then(() => {
                msgTopSuccess();
            })
            .catch(err => {
                msgError(err.response.data.message);
            });
    }

    
}