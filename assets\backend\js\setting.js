// 載入設定
async function loadSettings() {
    try {
        const res = await axiosRequest().get('/api/admin/settings');
        const data = res.data.data;

        const form = document.forms.main_form;
        form.com_name.value = data.com_name || '';
        form.com_email.value = data.com_email || '';
        form.bcc_mail.value = data.bcc_mail || '';
        form.pic_url.value = data.pic_url || '';
        form.smtp_ip.value = data.smtp_host || '';
        form.smtp_port.value = data.smtp_port || '25';
        form.smtp_ssl.checked = data.smtp_ssl === '1';
        form.use_sender.checked = data.smtp_use_sender === '1';
        form.smtp_user.value = data.smtp_username || '';
        form.smtp_pwd.value = data.smtp_password || '';

        // 重新初始化toggle
        if (typeof $().bootstrapToggle === 'function') {
            $('[data-toggle="toggle"]').bootstrapToggle('destroy').bootstrapToggle();
        }
    } catch (err) {
        console.log(err);
        msgError('載入設定失敗');
    }
}

async function updateSetting(debug = false) {

    const form = document.forms.main_form;
    const btns = form.querySelectorAll('button');
    const validator = $(form).validate();

    if (!validator.checkForm()) {
        validator.showErrors();
        validator.focusInvalid();
        return;
    }

    msgLoading();

    btns.forEach(btn => {
        btn.disabled = true;
    })

    try {
        await axiosRequest()
            .post('/api/admin/settings', {
                com_name: form.com_name.value,
                com_email: form.com_email.value,
                bcc_mail: form.bcc_mail.value,
                pic_url: form.pic_url.value,
                smtp_ip: form.smtp_ip.value,
                smtp_port: form.smtp_port.value,
                smtp_ssl: form.smtp_ssl.checked ? 1 : 0,
                use_sender: form.use_sender.checked ? 1 : 0,
                smtp_user: form.smtp_user.value,
                smtp_pwd: form.smtp_pwd.value,
                smtp_def: form.smtp_def.value,
            });

        msgTopSuccess('設定更新成功');

        if (debug) {
            await axiosRequest().get('/api/admin/smtp/debug');
            msgTopSuccess('測試郵件發送成功');
        }
    } catch (err) {
        console.log(err);
        msgError(err.response?.data?.msg || '操作失敗');
    } finally {
        btns.forEach(btn => {
            btn.disabled = false;
        });
    }
}

// 頁面載入時執行
$(document).ready(function() {
    loadSettings();
});