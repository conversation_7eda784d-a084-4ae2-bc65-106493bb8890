package backend_api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	. "cx/app/models"
	"cx/app/services"
	. "cx/database"
)

// GetSystemSettings 獲取設定
func GetSystemSettings(c *gin.Context) {
	conn := ConnectDB()
	defer CloseDB(conn)

	smtpSettings, err := GetSMTPSettings(conn)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "獲取設定失敗",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": smtpSettings,
	})
}

// UpdateSetting 更新設定
func UpdateSetting(c *gin.Context) {
	var request struct {
		ComName   string `form:"com_name" json:"com_name"`
		ComEmail  string `form:"com_email" json:"com_email"`
		BccMail   string `form:"bcc_mail" json:"bcc_mail"`
		PicURL    string `form:"pic_url" json:"pic_url"`
		SMTPHost  string `form:"smtp_ip" json:"smtp_ip"`
		SMTPPort  string `form:"smtp_port" json:"smtp_port"`
		SMTPSSL   string `form:"smtp_ssl" json:"smtp_ssl"`
		UseSender string `form:"use_sender" json:"use_sender"`
		SMTPUser  string `form:"smtp_user" json:"smtp_user"`
		SMTPPwd   string `form:"smtp_pwd" json:"smtp_pwd"`
		SMTPDef   string `form:"smtp_def" json:"smtp_def"`
	}

	if err := c.ShouldBind(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "資料錯誤",
			"error": err.Error(),
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	// 準備設定數據
	settings := map[string]string{
		"com_name":        request.ComName,
		"com_email":       request.ComEmail,
		"bcc_mail":        request.BccMail,
		"pic_url":         request.PicURL,
		"smtp_host":       request.SMTPHost,
		"smtp_port":       request.SMTPPort,
		"smtp_ssl":        request.SMTPSSL,
		"smtp_use_sender": request.UseSender,
		"smtp_username":   request.SMTPUser,
		"smtp_password":   request.SMTPPwd,
		"smtp_from":       request.ComEmail, // 使用公司郵箱作為發送者
	}

	// 批量更新設定
	if err := SetSettings(conn, settings); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "更新設定失敗",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"msg": "設定更新成功",
	})
}

// TestSMTP 測試SMTP設定
func TestSMTP(c *gin.Context) {
	conn := ConnectDB()
	defer CloseDB(conn)

	// 創建郵件服務
	emailService, err := services.NewEmailService(conn)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "郵件服務初始化失敗",
			"error": err.Error(),
		})
		return
	}

	// 獲取管理員信息
	adminInfo := GetAdminInfo(c)
	testEmail := adminInfo.AdminName // 使用管理員名稱作為測試郵箱

	// 發送測試郵件
	subject := "G.R.A.C.E 系統 - SMTP 測試郵件"
	body := `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>SMTP測試</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c3e50;">G.R.A.C.E 系統 - SMTP 測試</h2>
        <p>這是一封測試郵件，用於驗證 SMTP 設定是否正確。</p>
        <p>如果您收到這封郵件，表示 SMTP 設定已成功配置。</p>
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="font-size: 12px; color: #666;">
            此郵件由 G.R.A.C.E 系統自動發送。
        </p>
    </div>
</body>
</html>`

	err = emailService.SendEmail(testEmail, subject, body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "測試郵件發送失敗",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"msg": "測試郵件發送成功",
	})
}
