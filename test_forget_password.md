# G.R.A.C.E 忘記密碼功能測試文檔

## 功能概述

本文檔說明如何測試 G.R.A.C.E 學員系統的忘記密碼功能。該功能包括：

1. 會員申請重設密碼
2. 系統生成臨時密碼並發送郵件
3. 會員使用臨時密碼重設新密碼
4. 自動清理過期的臨時密碼

## 前置準備

### 1. 數據庫遷移

執行以下遷移文件來創建必要的數據表：

```bash
# 創建臨時密碼表
migrate -path database/migrations -database "mysql://user:password@tcp(localhost:3306)/grace" up

# 或者手動執行 SQL
mysql -u root -p grace < database/migrations/000221_create_member_temp_passwords_table.up.mysql
mysql -u root -p grace < database/migrations/000222_create_settings_table.up.mysql
```

### 2. SMTP 設定

#### 方式一：使用後台管理系統設定（推薦）

1. 登入後台管理系統
2. 進入「權限管理系統」→「網站設定」
3. 填寫 SMTP 設定：
   - 主機地址：localhost（如果使用本地 Postfix）
   - Port：25（或 587 for TLS）
   - SSL：根據需要啟用
   - 帳號/密碼：如果需要驗證
4. 點擊「儲存設定並測試發送信件」測試配置

#### 方式二：直接在數據庫設定

```sql
INSERT INTO settings (key, value) VALUES 
('smtp_host', 'localhost'),
('smtp_port', '25'),
('smtp_ssl', '0'),
('smtp_use_sender', '0'),
('smtp_username', ''),
('smtp_password', ''),
('smtp_from', '<EMAIL>');
```

### 3. 啟動排程服務（可選）

啟動排程服務來自動清理過期的臨時密碼：

```bash
cd scheduler
go run main.go
```

## 測試步驟

### 1. 測試申請重設密碼

#### API 測試

```bash
# 使用 curl 測試
curl -X GET "http://localhost:8081/api/members/forget?uid=<EMAIL>"
```

#### 前端測試

1. 訪問 `/member/forget` 頁面
2. 輸入會員帳號（Email）
3. 點擊「確認送出」

#### 預期結果

- 成功：返回 `{"msg": "發送成功請至您的信箱收取", "rtn": "/member/signin"}`
- 失敗：返回相應錯誤訊息

### 2. 檢查郵件發送

檢查指定的郵箱是否收到包含臨時密碼的郵件。郵件內容應包括：

- 會員姓名
- 8位隨機臨時密碼
- 30分鐘有效期提醒
- 登入連結

### 3. 測試使用臨時密碼重設密碼

#### API 測試

```bash
curl -X POST "http://localhost:8081/api/members/reset-password" \
  -H "Content-Type: application/json" \
  -d '{
    "uid": "<EMAIL>",
    "temp_password": "ABC12345",
    "new_password": "newpassword123"
  }'
```

#### 預期結果

- 成功：返回 `{"msg": "密碼重設成功", "rtn": "/member/signin"}`
- 失敗：返回相應錯誤訊息

### 4. 驗證密碼重設成功

使用新密碼嘗試登入系統，確認密碼已成功更新。

### 5. 測試臨時密碼過期

等待30分鐘後，再次嘗試使用同一個臨時密碼，應該返回「臨時密碼無效或已過期」錯誤。

## 數據庫檢查

### 查看臨時密碼記錄

```sql
SELECT * FROM member_temp_passwords ORDER BY created_at DESC;
```

### 查看系統設定

```sql
SELECT * FROM settings WHERE key LIKE 'smtp_%';
```

## 錯誤排除

### 1. 郵件發送失敗

- 檢查 SMTP 設定是否正確
- 確認 SMTP 服務是否運行
- 查看應用程式日誌

### 2. 臨時密碼無效

- 檢查臨時密碼是否已過期
- 確認臨時密碼是否已被使用
- 檢查會員ID是否正確

### 3. 數據庫錯誤

- 確認遷移文件已正確執行
- 檢查數據庫連接設定
- 查看數據庫錯誤日誌

## 安全考量

1. **臨時密碼有效期**：設定為30分鐘，平衡安全性和使用便利性
2. **一次性使用**：臨時密碼使用後立即標記為已使用
3. **自動清理**：排程任務定期清理過期的臨時密碼
4. **郵件安全**：使用HTML格式郵件，包含安全提醒
5. **密碼強度**：臨時密碼使用8位隨機字符，包含大小寫字母和數字

## SMTP 設定建議

### 在 GCP Compute Engine 上架設 Postfix

1. **安裝 Postfix**：
   ```bash
   sudo apt update
   sudo apt install postfix
   ```

2. **配置 Postfix**：
   - 選擇「Internet Site」
   - 設定郵件名稱為您的域名

3. **測試發送**：
   ```bash
   echo "Test email" | mail -s "Test Subject" <EMAIL>
   ```

4. **應用程式設定**：
   - SMTP Host: localhost
   - SMTP Port: 25
   - 無需驗證（本地發送）

### 設定建議

建議將 SMTP 設定放在**後台管理系統**中，原因：

1. **靈活性**：管理員可以隨時調整設定而無需重啟應用程式
2. **安全性**：敏感信息存儲在數據庫中，可以加密處理
3. **維護性**：不同環境可以有不同的設定
4. **測試功能**：內建測試郵件發送功能，方便驗證設定

## 總結

忘記密碼功能已完整實現，包括：

- ✅ 臨時密碼生成和存儲
- ✅ 郵件發送服務
- ✅ 密碼重設API
- ✅ 自動清理過期密碼
- ✅ 後台SMTP設定管理
- ✅ 安全性考量

系統現在可以安全地處理會員忘記密碼的情況，並提供良好的用戶體驗。
