package front_api

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"

	. "cx/app/models"
	. "cx/app/utils"
	. "cx/database"
)

func RegisterMember(c *gin.Context) {
	member := &Member{}
	reg := &MemReg{}

	if err := c.ShouldBind(&member); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "資料錯誤",
			"error": err.Error(),
		})
		return
	}

	if err := c.ShouldBind(&reg.Info); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "資料錯誤",
			"error": err.Error(),
		})
		return
	}

	if member.Uid == "" || member.Pwd == "" {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"msg": "帳號或密碼不可為空",
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	conn = conn.Begin()

	mem := &Member{}
	if conn.Unscoped().Select("id").Where("uid = ?", member.Uid).First(&mem); mem.ID != 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg": "帳號已存在",
		})
		return
	}

	if hashPwd, err := PasswordHash(member.Pwd); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "無法加密密碼",
			"error": err.Error(),
		})
		return
	} else {
		member.Pwd = hashPwd
	}

	member.Level = 1
	member.Status = "W"
	if err := conn.Create(&member).Error; err != nil {
		conn.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "註冊失敗，請聯絡官方",
			"error": err.Error(),
		})
		return
	}

	// 新增驗證
	reg.MemberID = member.ID
	if msg, err := reg.Init(conn, true); err != nil {
		fmt.Printf("RegisterMember Error: %s\n%s\n", msg, err.Error())
		conn.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "註冊失敗，請聯絡官方",
			"error": err.Error(),
		})
		return
	}

	conn.Commit()

	handleMemSignIn(c, member.ID)

	c.JSON(http.StatusOK, gin.H{
		"msg": "會員註冊成功",
		"rtn": "/member/register_ok",
	})
}

func SignInMember(c *gin.Context) {
	member := &Member{}
	request := &MemberLogin{}
	rtn := c.DefaultPostForm("rtn", "")

	if err := c.ShouldBind(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "資料錯誤",
			"error": err.Error(),
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	if err := conn.Select("id", "name", "uid", "pwd", "status").
		Where("uid = ?", request.Uid).First(&member).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "帳號錯誤",
			"error": err.Error(),
		})
		return
	}

	if isLogin := PasswordVerify(request.Pwd, member.Pwd); isLogin {
		if rtn == "" || member.Status == "W" {
			rtn = "/member/index"
		}

		if member.Status == "N" {
			// 停權
			c.JSON(http.StatusUnauthorized, gin.H{
				"msg": "帳號已遭停權! 請洽官方開通",
			})
			return
		}

		handleMemSignIn(c, member.ID)

		remind := checkMemberRemind(member.ID)

		c.JSON(http.StatusOK, gin.H{
			"msg":    "登入成功",
			"remind": remind,
			"rtn":    rtn,
		})
	} else {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg": "密碼錯誤",
		})
	}
}

func handleMemSignIn(c *gin.Context, memberID uint) {
	conn := ConnectDB()
	defer CloseDB(conn)

	conn.Model(&Member{ID: memberID}).Update("last_login", time.Now())

	session := sessions.Default(c)
	session.Options(sessions.Options{Path: "/", MaxAge: 48 * 3600}) // 48小時
	session.Set("member_id", memberID)
	session.Set("member_expired_at", time.Now().Add(48*time.Hour).Unix())
	session.Save()
}

func checkMemberRemind(memberID uint) []string {
	remind := []string{}

	conn := ConnectDB()
	defer CloseDB(conn)

	now := TimeToDate(time.Now())

	// check point expired at
	point := &Point{}
	if err := conn.Select("id", "points", "expired_at").
		Where("member_id = ?", memberID).Find(&point).Error; err != nil {
		fmt.Printf("checkMemberRemind Error: %s\n", err.Error())
	}

	if point.ID != 0 && point.Points > 0 && point.ExpiredAt.Valid {
		expiredAt := TimeToDate(point.ExpiredAt.Time)
		beforeMonth := expiredAt.AddDate(0, -1, -1)
		if (now.Before(expiredAt) || now.Equal(expiredAt)) && (now.After(beforeMonth) || now.Equal(beforeMonth)) {
			// 剩一個月，每次登入提醒
			remind = append(remind, fmt.Sprintf("課費將於 %s 到期，在此之前完成任意單位儲值可恢復使用", expiredAt.Format("2006/01/02")))

			if err := conn.Model(&Member{ID: memberID}).Update("point_ex_remind_at", time.Now()).Error; err != nil {
				fmt.Printf("checkMemberRemind Error: %s\n", err.Error())
			}
		}
	}

	// check member expired at
	member := &Member{}
	if err := conn.Select("id", "expired_at").Find(&member, memberID).Error; err != nil {
		fmt.Printf("checkMemberRemind Error: %s\n", err.Error())
	}

	if member.ID != 0 && !member.ExpiredAt.Time.IsZero() {
		expiredAt := TimeToDate(member.ExpiredAt.Time)
		beforeMonth := expiredAt.AddDate(0, -1, -1)
		if (now.Before(expiredAt) || now.Equal(expiredAt)) && (now.After(beforeMonth) || now.Equal(beforeMonth)) {
			// 剩一個月提醒
			member := &Member{}
			if err := conn.Select("id", "level_ex_remind_at").Find(&member, memberID).Error; err != nil {
				fmt.Printf("checkMemberRemind Error: %s\n", err.Error())
			}

			remindAt := TimeToDate(member.LevelExRemindAt.Time)
			// 確認是否已提醒過
			if !member.LevelExRemindAt.Valid ||
				now.AddDate(0, 0, -3).After(remindAt) || now.Equal(expiredAt.AddDate(0, 0, -1)) {
				// 每三天，或前一天提醒
				remind = append(remind, fmt.Sprintf("會員級別將於 %s 到期", expiredAt.Format("2006/01/02")))
				if err := conn.Model(&Member{ID: memberID}).Update("level_ex_remind_at", time.Now()).Error; err != nil {
					fmt.Printf("checkMemberRemind Error: %s\n", err.Error())
				}
			}
		}
	}

	return remind
}

func ForgetPassword(c *gin.Context) {
	member := &Member{}

	conn := ConnectDB()
	defer CloseDB(conn)

	if err := conn.Select("id", "status").Where("uid = ?", c.Query("uid")).Find(&member).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "無法取得會員資料",
			"error": err.Error(),
		})
		return
	}

	if member.ID == 0 {
		c.JSON(http.StatusOK, gin.H{
			"msg": "帳號或e-mail錯誤",
		})
		return
	}

	if member.Status != "Y" {
		c.JSON(http.StatusOK, gin.H{
			"msg": "您的帳號未啟用",
		})
		return
	}

	// TODO:寄信

	c.JSON(http.StatusOK, gin.H{
		"msg": "發送成功請至您的信箱收取",
		"rtn": "/member/signin",
	})
}

func ResendCertifyCode(c *gin.Context) {
	member := &Member{}

	conn := ConnectDB()
	defer CloseDB(conn)

	if err := conn.Select("id", "status").Where("uid = ?", c.Query("uid")).Find(&member).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "無法取得會員資料",
			"error": err.Error(),
		})
		return
	}

	if member.ID == 0 {
		c.JSON(http.StatusOK, gin.H{
			"msg": "帳號或e-mail錯誤",
		})
		return
	}

	if member.Status != "W" {
		c.JSON(http.StatusOK, gin.H{
			"msg": "您的帳號已開通，無需再認證",
		})
		return
	}

	// TODO:寄信

	c.JSON(http.StatusOK, gin.H{
		"msg": "發送成功請至您的信箱收取",
		"rtn": "/member/signin",
	})
}

func SignOutMember(c *gin.Context) {
	session := sessions.Default(c)
	session.Options(sessions.Options{MaxAge: -1})
	session.Clear()
	session.Save()

	c.JSON(http.StatusOK, gin.H{
		"msg": "登出成功",
		"rtn": "/member/signin",
	})
}

func GetMember(c *gin.Context) {
	member := &Member{}

	info := GetMemberInfo(c)

	if info.ID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg": "無法取得會員資料",
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	s := []string{
		"id", "uid", "name", "en_name", "nick_name", "sex", "phone", "line_id", "ig_id", "level", "expired_at", "id_number",
		"birthday_y", "birthday_m", "birthday_d", "country", "city", "area", "zipcode", "address",
	}

	if err := conn.Select(s).Find(&member, info.ID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "無法取得會員資料",
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": member,
	})
}

func UpdateMember(c *gin.Context) {
	member := &Member{}

	if err := c.ShouldBind(&member); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "資料有誤",
			"error": err.Error(),
		})
		return
	}

	info := GetMemberInfo(c)

	if member.ID != info.ID {
		c.JSON(http.StatusForbidden, gin.H{
			"msg": "權限不足",
		})
		return
	}

	conn := ConnectDB()
	defer CloseDB(conn)

	conn = conn.Begin()

	if member.Pwd != "" {
		hashPwd, err := PasswordHash(member.Pwd)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":   "更新密碼時發生錯誤",
				"error": err.Error(),
			})
			return
		}

		if err := conn.Model(&Member{ID: member.ID}).Update("pwd", hashPwd).Error; err != nil {
			conn.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{
				"msg":   "更新密碼時發生錯誤",
				"error": err.Error(),
			})
			return
		}
	}

	updated := []string{"name", "en_name", "nick_name", "sex", "phone", "line_id", "ig_id", "id_number",
		"birthday_y", "birthday_m", "birthday_d",
		"country", "city", "area", "zipcode", "address",
	}

	if err := conn.Select(updated).Updates(&member).Error; err != nil {
		conn.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{
			"msg":   "更新會員資料時發生錯誤",
			"error": err.Error(),
		})
		return
	}

	conn.Commit()

	c.JSON(http.StatusOK, gin.H{
		"msg": "變更成功",
	})
}

// 會員剩餘點數
func GetMemberPoints(c *gin.Context) {
	info := GetMemberInfo(c)

	if memPoint, err := MemberPoints(info.ID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "無法取得會員點數",
			"error": err.Error(),
		})
		return
	} else {
		c.JSON(http.StatusOK, gin.H{
			"data": memPoint,
		})
	}
}

func GetMemberSocialPointsLogs(c *gin.Context) {
	var (
		logs []struct {
			ID        uint   `json:"id"`
			Reason    string `json:"reason"`
			Points    int    `json:"points"`
			CreatedAt string `json:"created_at"`
		}
		points int
		page   PageOption
	)

	c.ShouldBind(&page)

	db := ConnectDB()
	defer CloseDB(db)

	info := GetMemberInfo(c)

	if err := db.Model(&SocialPoint{}).Select("points").
		Where("member_id = ?", info.ID).
		Where("year = YEAR(NOW())").
		Find(&points).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err,
			"msg":   "無法取得社群點數",
		})
		return
	}

	db = db.Where("member_id = ?", info.ID)

	GetPageCounts(db, SocialPointsLog{}, &page)

	if page.SortBy == "" {
		db = db.Order("social_points_logs.created_at DESC")
	} else {
		db = db.Order(page.SortBy + " " + page.GetSortType())
	}

	opt := []string{"social_points_logs.id AS id", "social_points_logs.points AS points",
		"social_points_logs.reason AS reason", "social_points_logs.created_at"}
	if err := db.Model(&SocialPointsLog{}).Select(opt).
		Find(&logs).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err,
			"msg":   "無法取得社群點數紀錄",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":   logs,
		"page":   page,
		"points": points,
	})
}

func GetMemberSocialPoints(c *gin.Context) {
	var data SocialPoint

	db := ConnectDB()
	defer CloseDB(db)

	info := GetMemberInfo(c)

	if err := db.Select("points").
		Where("member_id = ?", info.ID).
		Where("year = YEAR(NOW())").
		Find(&data).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err,
			"msg":   "無法取得社群點數",
		})
		return
	}

	c.JSON(http.StatusOK, data)
}
