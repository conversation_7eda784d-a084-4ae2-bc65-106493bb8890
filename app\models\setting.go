package models

import (
	"gorm.io/gorm"
)

type Setting struct {
	ID    uint   `gorm:"primaryKey" json:"id"`
	Key   string `gorm:"uniqueIndex;not null" json:"key"`
	Value string `json:"value"`
}

func (Setting) TableName() string {
	return "settings"
}

// GetSetting 獲取單個設定值
func GetSetting(conn *gorm.DB, key string) (string, error) {
	var setting Setting
	err := conn.Where("key = ?", key).First(&setting).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return "", nil
		}
		return "", err
	}
	return setting.Value, nil
}

// SetSetting 設定單個設定值
func SetSetting(conn *gorm.DB, key, value string) error {
	setting := Setting{Key: key, Value: value}
	return conn.Save(&setting).Error
}

// GetSettings 獲取多個設定值
func GetSettings(conn *gorm.DB, keys []string) (map[string]string, error) {
	var settings []Setting
	err := conn.Where("key IN (?)", keys).Find(&settings).Error
	if err != nil {
		return nil, err
	}

	result := make(map[string]string)
	for _, setting := range settings {
		result[setting.Key] = setting.Value
	}

	return result, nil
}

// SetSettings 批量設定多個設定值
func SetSettings(conn *gorm.DB, settings map[string]string) error {
	tx := conn.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for key, value := range settings {
		setting := Setting{Key: key, Value: value}
		if err := tx.Save(&setting).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// SMTPSettings SMTP相關設定結構
type SMTPSettings struct {
	Host      string `json:"smtp_host" form:"smtp_host"`
	Port      string `json:"smtp_port" form:"smtp_port"`
	Username  string `json:"smtp_username" form:"smtp_username"`
	Password  string `json:"smtp_password" form:"smtp_password"`
	UseSSL    string `json:"smtp_ssl" form:"smtp_ssl"`
	UseSender string `json:"smtp_use_sender" form:"smtp_use_sender"`
	From      string `json:"smtp_from" form:"smtp_from"`
}

// GetSMTPSettings 獲取SMTP設定
func GetSMTPSettings(conn *gorm.DB) (*SMTPSettings, error) {
	keys := []string{
		"smtp_host", "smtp_port", "smtp_username", "smtp_password",
		"smtp_ssl", "smtp_use_sender", "smtp_from",
	}

	settingsMap, err := GetSettings(conn, keys)
	if err != nil {
		return nil, err
	}

	return &SMTPSettings{
		Host:      settingsMap["smtp_host"],
		Port:      settingsMap["smtp_port"],
		Username:  settingsMap["smtp_username"],
		Password:  settingsMap["smtp_password"],
		UseSSL:    settingsMap["smtp_ssl"],
		UseSender: settingsMap["smtp_use_sender"],
		From:      settingsMap["smtp_from"],
	}, nil
}
