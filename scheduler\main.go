package main

import (
	"cx/app/tasks"
	"log"
	"time"

	"github.com/spf13/viper"
)

func init() {
	viper.AddConfigPath("../")
	viper.SetConfigFile(".env")
	if err := viper.ReadInConfig(); err != nil {
		panic(err)
	}

	twTimeZone, err := time.LoadLocation("Asia/Taipei")

	if err != nil {
		panic(err)
	}

	time.Local = twTimeZone
}

type SchedulerInterface interface {
	Start(tSpec string) error
	Stop()
}

type Scheduler struct {
	name      string
	spec      string // Add a new field to store the cron time spec
	scheduler SchedulerInterface
}

func main() {
	schedulers := []Scheduler{
		// 設定每天凌晨 00:01 執行
		{"會員級別逾期檢查", "0 1 0 * * *", tasks.NewMemberLevelScheduler()},
		{"點數逾期檢查", "0 1 0 * * *", tasks.NewPointScheduler()},
		// 設定每天凌晨 00:02 執行
		// {"輔導任務檢查", "0 2 0 * * *", tasks.NewCheckCounselTaskScheduler()},
		{"個人進修課表逾期檢查", "0 2 0 * * *", tasks.NewStudyPlanScheduler()},
		// 設定每天凌晨 00:05 執行
		{"挑戰任務逾期檢查", "0 5 0 * * *", tasks.NewChallengeStatisticScheduler()},
	}

	for _, s := range schedulers {
		startScheduler(&s)
		defer s.scheduler.Stop()
	}

	select {}
}

func startScheduler(s *Scheduler) {
	if err := s.scheduler.Start(s.spec); err != nil {
		log.Printf("%s 啟動失敗: %v", s.name, err)
	}
}
