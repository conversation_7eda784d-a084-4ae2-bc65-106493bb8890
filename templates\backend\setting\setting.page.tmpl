<!DOCTYPE html>
<html>
    <head>
        {{template "head" .}}
        {{template "bootstrap_toggle" .}}
        {{template "sweet_alert" .}}
        {{template "validate" .}}
        <script>
            $(document).ready(function() {
                $('#main_form').validate();
            });
        </script>

        <script src="/admin/assets/js/setting.min.js"></script>
    </head>

    <body>
        {{template "top" .}}

        {{block "setting" .}}{{end}}

        {{template "bottom" .}}
    </body>
</html>

{{define "setting"}}
    <div class="content_box">
        <form id="main_form" action="javascript:void(0);">
            {{ .csrf_field }}

            <div class="card">
                <div class="card-header">網站資料設定</div>
                <div class="card-body">
                    <div class="row mb-3">
                        <label class="col-md-2 col-sm-4 col-form-label text-md-end">＊公司名稱</label>
                        <div class="col-sm-10">
                            <input type="text" name="com_name" maxlength="50" class="form-control" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-md-2 col-sm-4 col-form-label text-md-end">＊公司信箱</label>
                        <div class="col-sm-10">
                            <input type="text" name="com_email" maxlength="50" class="form-control" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-md-2 col-sm-4 col-form-label text-md-end">BCC</label>
                        <div class="col-sm-10">
                            <input type="text" value="說明：設定密件請一行輸入一個E-mail。" class="form-control-plaintext" style="color:green;" readonly>
                            <textarea name="bcc_mail" rows="2" cols="20" class="form-control"></textarea>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-md-2 col-sm-4 col-form-label text-md-end">＊目前網址</label>
                        <div class="col-sm-10">
                            <input type="text" name="pic_url" maxlength="50" class="form-control" required>
                            <input type="text" value="編輯器圖片路徑用" class="form-control-plaintext" readonly>
                        </div>
                    </div>

                    <div class="card-header mb-3">SMTP設定</div>
                    <div class="row mb-3">
                        <label class="col-md-2 col-sm-4 col-form-label text-md-end">＊主機地址</label>
                        <div class="col-sm-10">
                            <input type="text" name="smtp_ip" maxlength="100" class="form-control" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-md-2 col-sm-4 col-form-label text-md-end">＊Port</label>
                        <div class="col-sm-10">
                            <input type="text" name="smtp_port" maxlength="100" class="form-control" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-md-2 col-sm-4 col-form-label text-md-end">SSL</label>
                        <div class="col-sm-10">
                            <label class="form-check-inline">
                                <input type="checkbox" name="smtp_ssl" data-toggle="toggle" data-onlabel="啟用" data-offlabel="未啟用" data-onstyle="success">
                                (若啟用請確認您填的Port是否正確)
                            </label>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-md-2 col-sm-4 col-form-label text-md-end">帳號</label>
                        <div class="col-sm-10">
                            <input type="text" name="smtp_user" maxlength="50" class="form-control inline">
                            <label class="form-check form-check-inline">
                                <input type="checkbox" name="use_sender" placeholder="有需要才填" class="form-check-input">
                                <span class="fw-bold">用此帳號作為寄件者</span>
                            </label>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-md-2 col-sm-4 col-form-label text-md-end">密碼</label>
                        <div class="col-sm-10">
                            <input type="text" name="smtp_pwd" maxlength="50" placeholder="有需要才填" class="form-control inline">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-md-2 col-sm-4 col-form-label text-md-end">使用預設驗證</label>
                        <div class="col-sm-10">
                            <select name="smtp_def" class="form-control form-select">
                                <option value="" selected>不設定(視您的SMTP主機設定來調整)</option>
                                <option value="Y">是</option>
                                <option value="N">否</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <label class="col-md-2 col-sm-4 col-form-label"></label>
                        <div class="col-sm-10">
                            <button type="button" onclick="updateSetting(true)" class="btn btn-outline-secondary">
                                <i class="fa-solid fa-envelope"></i>
                                儲存設定並測試發送信件
                            </button>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-md-2 col-sm-4 col-form-label"></label>
                        <div class="col-sm-10">
                            <button type="button" onclick="updateSetting(false)" class="btn btn-outline-secondary">
                                儲存設定
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
{{end}}