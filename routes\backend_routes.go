package routes

import (
	. "cx/app/controllers/backend"
	"cx/app/middleware"
	"net/http"
	"sync/atomic"

	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"

	. "cx/app/controllers/api"
	. "cx/app/controllers/api/backend_api"
	. "cx/app/middleware"
)

var backendUserCount int32

func InitBackendRouter() *gin.Engine {
	router := gin.Default()

	router.LoadHTMLGlob("templates/backend/**/*")
	router.Static("/admin/assets", "./assets/backend")
	router.Static("/common", "./assets/common")
	router.Static("/upload", "./assets/upload")
	router.Static("/uploads", "./uploads")
	router.Static("/checksum", "./checksum")

	store := cookie.NewStore([]byte("Taiwan is No.1!@!@"))
	router.Use(sessions.Sessions("my-back-session", store))

	config := cors.DefaultConfig()
	config.AllowOrigins = []string{
		"http://localhost:8089",
		"http://graceschool.com.tw", "https://graceschool.com.tw", "https://www.graceschool.com.tw",
	} // Add your allowed origins
	config.AllowMethods = []string{"GET", "POST", "PUT", "PUSH", "DELETE"}
	router.Use(cors.New(config))

	router.GET("/user-count", func(c *gin.Context) {
		frontendCount := atomic.LoadInt32(&frontendUserCount)
		backendCount := atomic.LoadInt32(&backendUserCount)
		c.JSON(http.StatusOK, gin.H{
			"status":      "ok",
			"connections": frontendCount + backendCount,
		})
	})

	router.GET("/api/checksum", GetChecksum)

	api := router.Group("/api")

	// router.NoRoute(func(c *gin.Context) {
	// 	c.Redirect(http.StatusFound, "/admin/index")
	// })

	router.Use(UserCountMiddleware(&backendUserCount))

	backendRouters(router.Group("/admin"))

	backendApiRouters(api.Group("/admin"))

	return router
}

func backendRouters(router *gin.RouterGroup) {
	router.GET("/", LoginAdminPage).
		GET("/signout", SignOutAdminPage)

	router.Use(RequireAdminLogin("page"))

	router.GET("/index", BackendHomePage)

	fileupload := router.Group("/fileupload")
	fileupload.GET("/index", FileUploadIndexPage)

	user := router.Group("/user")
	user.GET("/pwd", ChangePwdPage).
		Use(RequireIsAdmin("web")).
		GET("/index", UserIndexPage)

	page := router.Group("/page")
	page.GET("/reg", PageRegPage).
		GET("/range", PageRangePage)

	setting := router.Group("/setting")
	setting.GET("/", SettingPage).
		POST("/", UpdateSetting) // api

	member := router.Group("/member")
	member.GET("/", MemberPage).
		GET("/reg/:id", MemberRegPage).
		GET("/overview", MemberOverviewPage).
		GET("/product", MemberProductPage).
		GET("/watch", MemberWatchPage).
		GET("/counsel", MemberCounselPage).
		GET("/goods", MemberGoodsPage).
		GET("/point", MemberPointPage).
		GET("/credit", MemberCreditPage).
		GET("/know", MemberKnowPage).
		GET("/register", MemberVerifyPage).
		GET("/challenge", MemberChallengePage).
		GET("/membership", MembershipApplyPage).
		GET("/study-plan", MemberStudyPlanPage).
		// 入學申請
		GET("/applicants", RegisterApplicantsPage).
		GET("/real-quests", RegisterRealQuestsPage).
		GET("/real-quests/create", RegisterRealQuestCreatePage).
		GET("/real-quests/:id/edit", RegisterRealQuestEditPage).
		GET("/real-items", RegisterRealItemsPage).
		GET("/real-items/create", RegisterRealItemCreatePage).
		GET("/real-items/:id/edit", RegisterRealItemEditPage)

	social := router.Group("/social")
	{
		social.GET("/reg", SocialRegPage)
		social.GET("/task", SocialTaskPage)
		social.GET("/exchange", SocialExchangePage)
		social.GET("/history", SocialLogPage)
	}

	counsel := router.Group("/counsel")
	counsel.GET("/setting", CounselSettingPage).
		GET("/reg", CounselRegPage).
		GET("/reg/:counsel_id", CounselRegPage).
		GET("/index", CounselIndexPage).
		GET("/calendar", CounselCalendarPage)

	product := router.Group("/product")
	product.GET("/kind_reg", ProductKindRegPage).
		GET("/kind_range", ProductKindRangePage).
		GET("/reg", ProductRegPage).
		GET("/index", ProductIndexPage).
		GET("/range", ProductRangePage).
		GET("/pro_reg", ProductManualRegPage).
		GET("/order/:id", ProductOrderPage). // :id is product id
		GET("/remind", ProductRemindPage).
		GET("/report", ProductReportPage).
		GET("/live", ProductLivePage)

	work := router.Group("/work")
	work.GET("/", WorkIndexPage).
		GET("/reg", WorkRegPage).
		GET("/order/:id", WorkOrderPage)

	activity := router.Group("/activity")
	activity.GET("/index", ActivityIndexPage).
		GET("/reg", ActivityRegPage)

	order := router.Group("/order")
	order.GET("/", OrderPage).
		GET("/:id", OrderDataPage).
		GET("/payment", OrderPaymentPage).
		GET("/remind", OrderRemindPage)

	point := router.Group("/point")
	point.GET("/", PointDataPage).
		GET("/range", PointRangePage).
		GET("/history", PointHistoryPage).
		GET("/charge", PointChargePage)

	coupon := router.Group("/coupon")
	coupon.GET("/index", CouponIndexPage).
		GET("/reg", CouponRegPage).
		GET("/reg/:id", CouponRegPage).
		GET("/history", CouponHistoryPage).
		GET("/transfer", CouponTransferPage)

	goods := router.Group("/goods")
	goods.GET("/index", GoodsIndexPage).
		GET("/reg", GoodsRegPage).
		GET("/reg/:id", GoodsRegPage).
		GET("/goods_reg", GoodsManualRegPage).
		GET("/report", GoodsReportPage)

	challenge := router.Group("/challenge")
	{
		challenge.GET("/level", ChallengeLevelPage)
		challenge.GET("/progress", ChallengeProgressPage)
		challenge.GET("/reg", ChallengeRegPage)
		challenge.GET("/index", ChallengeIndexPage)
		challenge.GET("/overview", ChallengeOverviewPage)
		challenge.GET("/study-plan", StudyPlanOverviewPage)
	}
}

func backendApiRouters(api *gin.RouterGroup) {
	api.Use(middleware.ErrorLogger())

	api.POST("/signin", SignInAdmin)

	api.Use(RequireAdminLogin("api"))

	api.POST("/uploadfile", UploadFile)

	user := api.Group("/users")
	user.PATCH("/pwd", UpdateAdminPwd).
		Use(RequireIsAdmin("api")).
		GET("/", GetUsers).
		POST("/", CreateUser).
		PATCH("/:user_id/status", UpdateUserStatus).
		PATCH("/:user_id/is-admin", UpdateUserIsAdmin).
		DELETE("/:user_id", DeleteUser)

	setting := api.Group("/settings")
	setting.GET("/", GetSystemSettings).
		POST("/", UpdateSetting)

	smtp := api.Group("/smtp")
	smtp.GET("/debug", TestSMTP)

	page := api.Group("/pages")
	page.GET("/", GetPages).
		GET("/:id", GetPage).
		GET("/conts/:id", GetPageContent).
		PATCH("/:id", UpdatePage).
		PATCH("/conts/:id", UpdatePageContent).
		PATCH("/sorting", UpdatePageSorting)

	remind := api.Group("/reminds")
	remind.GET("/goods", GetGoodsRemindList).
		GET("/counsel", GetCounselReminds).
		GET("/verify", GetMemberVerifyRemindList).
		GET("/order", GetOrderRemindList).
		GET("/memberships", GetMembershipRemindList).
		GET("/study-plans", GetStudyPlanRemindList).
		GET("/study-plans/alert", GetAlertStudyPlanRemindList)

	member := api.Group("/members")
	member.GET("/", GetMembers).
		GET("/:id", GetMember).
		GET("/export", ExportMembers).
		POST("/import", ImportMembers).
		PATCH("/:id", UpdateMemberData).
		PATCH("/pwd/:id", UpdateMemberPwd).
		PATCH("/status/:id", UpdateMemberStatus).
		PATCH("/points/expired/:id", UpdateMemberPointExpired).
		DELETE("/", DeleteMembers).
		DELETE("/:id", DeleteMember).
		PATCH("/works/:mem_work_id/deadline", UpdateMemberWorkDeadline).
		GET("/goods/:member_id", GetMemberGoodsList).
		GET("/goods/records/:member_id", GetMemberGoodsRecords).
		GET("/goods/expired/:member_id", GetMemberExpiredGoods).
		DELETE("/goods/:mem_goods_id", DeleteMemberGoods).
		PATCH("/goods/status/:id", UpdateMemberGoodsStatus).
		PATCH("/products/status/:mem_pro_id", UpdateMemberProductStatus).
		PATCH("/products/:mem_pro_id", UpdateMemberProduct).
		GET("/overview/:member_id", GetMemberOverview).
		GET("/overview/:member_id/export", ExportMemberOverview).
		PATCH("products/:mem_pro_id/counsel-deadline", UpdateMemberProductCounselDeadline).
		GET("/watch", GetMemberWatch)

	member.Use(ResumeStopProducts).
		GET("/products/:member_id", GetMemberProducts).
		GET("/works", GetMemberWorkList)

	social := api.Group("/socials")
	{
		social.POST("/", CreateSocialPoints)

		// log
		log := social.Group("/logs")
		log.GET("/", GetSocialPointsLogList)
		log.DELETE("/:id", DeleteSocialPointsLog)

		// task
		task := social.Group("/tasks")
		task.GET("/", GetSocialTaskList)
		task.GET("/:id", GetSocialTask)
		task.POST("/", CreateSocialTask)
		task.PATCH("/:id", UpdateSocialTask)
		task.PATCH("/:id/status", UpdateSocialTaskStatus)
		task.DELETE("/:id", DeleteSocialTask)

		// exchange
		exchange := social.Group("/exchanges")
		exchange.GET("/", GetSocialExchangeList)
		exchange.GET("/:id", GetSocialExchange)
		exchange.POST("/", CreateSocialExchange)
		exchange.PATCH("/:id", UpdateSocialExchange)
		exchange.PATCH("/:id/status", UpdateSocialExchangeStatus)
		exchange.DELETE("/:id", DeleteSocialExchange)
	}

	levelApplyOption := api.Group("/level-apply/options")
	{
		levelApplyOption.GET("/", GetLevelApplyOption)
		levelApplyOption.PATCH("/", UpdateLevelApplyOption)
	}

	challenge := api.Group("/challenges")
	{
		challenge.GET("/", GetChallengeTasks)
		challenge.GET("/:id", GetChallengeTask)
		challenge.GET("/:id/info", GetChallengeTaskInfo)
		challenge.POST("/", CreateChallengeTask)
		challenge.PATCH("/:id", UpdateChallengeTask)
		challenge.PATCH("/:id/status", UpdateChallengeTaskStatus)
		challenge.DELETE("/:id", DeleteChallengeTask)

		challenge.GET("/overview", GetChallengeOverview)
	}

	challengeProgress := api.Group("/challenge-progresses")
	{
		challengeProgress.GET("/", GetChallengeProgresses)
		challengeProgress.GET("/:id", GetChallengeProgress)
		challengeProgress.PATCH("/:id", UpdateChallengeProgress)
	}

	membership := api.Group("/memberships")
	{
		membership.POST("/:id/accept", AcceptMembershipApply)
		membership.POST("/:id/reject", RejectMembershipApply)

		membership.POST("/:id/complete", CompleteMembershipApply)

		membership.GET("/", GetMembershipApplications)
		membership.PATCH("/:id/exam", UpdateMembershipExam)
		membership.PATCH("/:id/stored", UpdateMembershipStored)
	}

	membershipApplyDate := membership.Group("/apply-dates")
	{
		membershipApplyDate.GET("/", GetMembershipApplyDates)
		membershipApplyDate.POST("/", CreateMembershipApplyDate)
		membershipApplyDate.DELETE("/:id", DeleteMembershipApplyDate)
	}

	studyPlan := api.Group("/study-plans")
	{
		studyPlan.POST("/:id/accept", AcceptStudyPlan)
		studyPlan.POST("/:id/complete", CompleteStudyPlan)
		studyPlan.POST("/:id/reject", RejectStudyPlan)

		studyPlan.GET("/", GetStudyPlanApplications)
		studyPlan.POST("/", CreateStudyPlan)
		studyPlan.DELETE("/:id", DeleteStudyPlan)
		studyPlan.DELETE("/", DeleteStudyPlans)
		studyPlan.GET("/overview", GetStudyPlanOverview)
		studyPlan.GET("/export", ExportStudyPlanOverview)
		studyPlan.POST("/tasks", CreateStudyTask)
		studyPlan.PATCH("/tasks/:id", UpdateStudyTask)
		studyPlan.DELETE("/tasks/:id", DeleteStudyTask)
	}

	// verify
	memberVerify := member.Group("/verify")
	memberVerify.GET("/", GetMemberVerify).
		PATCH("/status", UpdateMemberVerifyStatus).
		PATCH("/info", UpdateMemberVerifyInfo).
		PATCH("/official", UpdateMemberVerifyOfficial).
		PATCH("/real", UpdateMemberVerifyReal).
		PATCH("/quiz", UpdateRegVerifyQuiz).
		GET("/histories", GetMemberVerifyHistories).
		PATCH("/report", UpdateMemberVerifyReport).
		PATCH("/report/status", UpdateMemberVerifyReportStatus).
		PATCH("/report/note", UpdateMemberVerifyReportNote).
		Use(HandleOverdueReport).
		GET("/reports", GetMemberVerifyReports)
	verifyReal := memberVerify.Group("/real")
	verifyReal.GET("quests/", GetMemberVerifyRealQuests).
		GET("/quests/:id", GetMemberVerifyRealQuest).
		POST("/quests", CreateMemberVerifyRealQuest).
		PATCH("/quests/:id", UpdateMemberVerifyRealQuest).
		DELETE("/quests/:id", DeleteMemberVerifyRealQuest).
		GET("/items", GetRealQuestItems).
		GET("/items/:id", GetRealQuestItem).
		POST("/items", CreateRealQuestItem).
		PATCH("/items/:id", UpdateRealQuestItem).
		DELETE("/items/:id", DeleteRealQuestItem)

	// register applicants
	registerApi := api.Group("/register")
	registerApi.GET("/applicants", GetRegisterApplicants)

	product := api.Group("/products")
	product.Use(DownExpiredProducts).
		GET("/", GetProducts).
		GET("/:id", GetProduct).
		GET("/export", ExportProducts).
		POST("/", CreateProduct).
		POST("/reg", CreateProductManual).
		PATCH("/:id", UpdateProduct).
		PATCH(":id/status", UpdateProductStatus).
		PATCH("/sorting", UpdateProductSorting).
		DELETE("/", DeleteProducts).
		DELETE("/:id", DeleteProduct).
		POST("/upload/img/:id", UploadProductImg).
		DELETE("/orders", DeleteProductOrders).
		GET("/orders/export", ExportProductOrders).
		POST("/orders/import", ImportProductOrders).
		GET("/search", GetProductSearch).
		GET("/conts", GetProductContent).
		PATCH("/conts", UpdateProductContent).
		GET("/reports", GetProductReports)

	product_kind := product.Group("/kind")
	product_kind.GET("/", GetProductKinds).
		GET("/relations", GetProKindRelations).
		GET("/:id", GetProductKind).
		GET("/children/:id", GetProductKindChildren).
		POST("/", CreateProductKind).
		PATCH("/:id", UpdateProductKind).
		PATCH("/sorting", UpdateProductKindSorting).
		DELETE("/:id", DeleteProductKind)

	point := api.Group("/points")
	point.GET("/", GetPointHistories).
		GET("/:id", GetPoints).
		GET("/export", ExportPointHistories).
		POST("/import", ImportPointHistories).
		POST("/:id", CreateMemberPoint).
		DELETE("/", DeletePointHistories).
		DELETE("/:id", DeletePointHistory)

	stored := point.Group("/stored")
	stored.GET("/", GetPointStoredList).
		GET("/:id", GetPointStored).
		POST("/", CreatePointStored).
		PATCH("/:id", UpdatePointStored).
		PATCH("/sorting", UpdatePointStoredSorting).
		DELETE("/:id", DeletePointStored)

	activity := api.Group("/activity")
	activity.GET("/", GetActivity).
		GET("/:id", GetActivity).
		POST("/", CreateActivity).
		PATCH("/:id", UpdateActivity).
		PATCH("/:id/status", UpdateActivityStatus).
		DELETE("/:id", DeleteActivity)

	order := api.Group("/orders")
	order.GET("/", GetOrders).
		GET("/:id", GetOrder).
		GET("/export", ExportOrders).
		POST("/import", ImportOrders).
		PATCH("/pay_status/:id", UpdateOrderStatus).
		PATCH("/:id/note", UpdateOrderNote).
		DELETE("/", DeleteOrders).
		DELETE("/:id", DeleteOrder)

	payment_options := order.Group("/payment_options")
	payment_options.GET("/", GetPaymentOptions).
		GET("/:id", GetPaymentOptionByID).
		POST("/", CreatePaymentOption).
		PATCH("/:id", UpdatePaymentOption).
		DELETE("/:id", DeletePaymentOption).
		PATCH("/sorting", UpdatePaymentOptionSorting)

	work := api.Group("/works")
	work.GET("/", GetWorkList).
		GET("/:id", GetWork).
		GET("/export", ExportWorks).
		DELETE("/", DeleteWorks).
		DELETE("/:id", DeleteWork).
		GET("/order/:id", GetWorkOrderList).
		GET("/orders/export", ExportWorkOrders).
		POST("/orders/import", ImportWorkOrders).
		PATCH("/order/status/:id", UpdateWorkOrderStatus).
		DELETE("/order/:id", DeleteWorkOrder).
		POST("/", CreateWork).
		PATCH("/:id", UpdateWork).
		POST("/upload/:id", UploadWork)

	coupon := api.Group("/coupons")
	coupon.GET("/", GetCoupons).
		GET("/:id", GetCoupon).
		POST("/", CreateCoupon).
		PATCH("/:id", UpdateCoupon).
		DELETE("/:id", DeleteCoupon).
		GET("/order/", GetCouponOrders).
		GET("/transfer", GetCouponTransferRecords)

	goods := api.Group("/goods")
	goods.GET("/", GetGoodsList).
		GET("/:id", GetGoods).
		POST("/", CreateGoods).
		POST("/reg", CreateGoodsManual).
		POST("/upload/:id", UploadGoodsImg).
		POST("/apply/:mem_goods_id", ApplyGoods).
		POST("/deny/:mem_goods_id", DenyGoods).
		PATCH("/:id", UpdateGoods).
		DELETE("/", DeleteGoodsList).
		DELETE("/:id", DeleteGoods).
		GET("/report", GetGoodsReports)

	counsel := api.Group("/counsels")
	counsel.GET("/settings", GetCounselSettings).
		POST("/settings", UpdateCounselSettings).
		POST("/", CreateCounselAppointment).
		GET("/", GetCounselAppointments).
		GET("/:counsel_id", GetCounselAppointment).
		PATCH("/:counsel_id", UpdateCounselAppointment).
		DELETE("/", DeleteCounselAppointments).
		POST("/import", ImportCounsels).
		GET("/export", ExportCounsels)

	meeting := api.Group("/meetings")
	meeting.GET("/:counsel_id", GetMeeting).
		POST("/", CreateMeeting).
		DELETE("/:counsel_id", DeleteMeeting).
		GET("/users", GetZoomUsers)
}
