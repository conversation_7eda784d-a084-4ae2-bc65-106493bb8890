package services

import (
	"crypto/tls"
	"fmt"
	"net/smtp"
	"strconv"

	"gorm.io/gorm"
)

type SMTPConfig struct {
	Host      string
	Port      int
	Username  string
	Password  string
	UseSSL    bool
	UseSender bool
	From      string
}

type EmailService struct {
	config *SMTPConfig
}

// NewEmailService 創建郵件服務實例
func NewEmailService(conn *gorm.DB) (*EmailService, error) {
	config, err := getSMTPConfig(conn)
	if err != nil {
		return nil, fmt.Errorf("獲取SMTP配置失敗: %v", err)
	}

	return &EmailService{config: config}, nil
}

// getSMTPConfig 從數據庫或環境變量獲取SMTP配置
func getSMTPConfig(conn *gorm.DB) (*SMTPConfig, error) {
	// 嘗試從數據庫獲取設定
	var settings []struct {
		Key   string `gorm:"column:key"`
		Value string `gorm:"column:value"`
	}

	err := conn.Table("settings").
		Where("`key` IN ('smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_ssl', 'smtp_use_sender', 'smtp_from')").
		Find(&settings).Error

	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("查詢設定失敗: %v", err)
	}

	config := &SMTPConfig{}
	settingsMap := make(map[string]string)

	for _, setting := range settings {
		settingsMap[setting.Key] = setting.Value
	}

	// 如果數據庫中有配置，使用數據庫配置
	if len(settingsMap) > 0 {
		config.Host = settingsMap["smtp_host"]
		if port, err := strconv.Atoi(settingsMap["smtp_port"]); err == nil {
			config.Port = port
		}
		config.Username = settingsMap["smtp_username"]
		config.Password = settingsMap["smtp_password"]
		config.UseSSL = settingsMap["smtp_ssl"] == "1" || settingsMap["smtp_ssl"] == "true"
		config.UseSender = settingsMap["smtp_use_sender"] == "1" || settingsMap["smtp_use_sender"] == "true"
		config.From = settingsMap["smtp_from"]
	} else {
		// 如果數據庫中沒有配置，使用預設配置（可以從環境變量讀取）
		config.Host = "localhost"
		config.Port = 25
		config.Username = ""
		config.Password = ""
		config.UseSSL = false
		config.UseSender = false
		config.From = "<EMAIL>"
	}

	// 驗證必要配置
	if config.Host == "" {
		return nil, fmt.Errorf("SMTP主機地址未設定")
	}

	if config.Port == 0 {
		config.Port = 25 // 預設端口
	}

	return config, nil
}

// SendEmail 發送郵件
func (es *EmailService) SendEmail(to, subject, body string) error {
	from := es.config.From
	if es.config.UseSender && es.config.Username != "" {
		from = es.config.Username
	}

	// 構建郵件內容
	msg := fmt.Sprintf("From: %s\r\nTo: %s\r\nSubject: %s\r\nContent-Type: text/html; charset=UTF-8\r\n\r\n%s",
		from, to, subject, body)

	// 連接SMTP服務器
	addr := fmt.Sprintf("%s:%d", es.config.Host, es.config.Port)

	var auth smtp.Auth
	if es.config.Username != "" && es.config.Password != "" {
		auth = smtp.PlainAuth("", es.config.Username, es.config.Password, es.config.Host)
	}

	var err error
	if es.config.UseSSL {
		err = es.sendMailTLS(addr, auth, from, []string{to}, []byte(msg))
	} else {
		err = smtp.SendMail(addr, auth, from, []string{to}, []byte(msg))
	}

	if err != nil {
		return fmt.Errorf("發送郵件失敗: %v", err)
	}

	return nil
}

// sendMailTLS 使用TLS發送郵件
func (es *EmailService) sendMailTLS(addr string, auth smtp.Auth, from string, to []string, msg []byte) error {
	// 建立TLS連接
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
		ServerName:         es.config.Host,
	}

	conn, err := tls.Dial("tcp", addr, tlsConfig)
	if err != nil {
		return err
	}
	defer conn.Close()

	client, err := smtp.NewClient(conn, es.config.Host)
	if err != nil {
		return err
	}
	defer client.Quit()

	if auth != nil {
		if err = client.Auth(auth); err != nil {
			return err
		}
	}

	if err = client.Mail(from); err != nil {
		return err
	}

	for _, addr := range to {
		if err = client.Rcpt(addr); err != nil {
			return err
		}
	}

	w, err := client.Data()
	if err != nil {
		return err
	}

	_, err = w.Write(msg)
	if err != nil {
		return err
	}

	err = w.Close()
	if err != nil {
		return err
	}

	return nil
}

// SendForgetPasswordEmail 發送忘記密碼郵件
func (es *EmailService) SendForgetPasswordEmail(to, memberName, tempPassword string) error {
	subject := "G.R.A.C.E 學員系統 - 密碼重設通知"

	body := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>密碼重設通知</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c3e50;">G.R.A.C.E 學員系統 - 密碼重設</h2>
        
        <p>親愛的 %s 學員，您好：</p>
        
        <p>您已申請重設密碼，以下是您的臨時密碼：</p>
        
        <div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;">
            <strong style="font-size: 18px; color: #007bff;">臨時密碼：%s</strong>
        </div>
        
        <p><strong>重要提醒：</strong></p>
        <ul>
            <li>此臨時密碼有效期限為 <strong>30分鐘</strong></li>
            <li>請儘快使用此臨時密碼登入系統並修改您的密碼</li>
            <li>為了您的帳號安全，請勿將此密碼告知他人</li>
            <li>如果您沒有申請密碼重設，請忽略此郵件</li>
        </ul>
        
        <p>登入網址：<a href="https://graceschool.com.tw/member/signin" style="color: #007bff;">https://graceschool.com.tw/member/signin</a></p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        
        <p style="font-size: 12px; color: #666;">
            此郵件由系統自動發送，請勿直接回覆。<br>
            如有任何問題，請聯絡 G.R.A.C.E 客服團隊。
        </p>
    </div>
</body>
</html>
`, memberName, tempPassword)

	return es.SendEmail(to, subject, body)
}
